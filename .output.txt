Requirement already satisfied: langchain==0.3.25 in c:\users\<USER>\documents\github\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 1)) (0.3.25)
Requirement already satisfied: langchain-community==0.3.24 in c:\users\<USER>\documents\github\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 2)) (0.3.24)
Requirement already satisfied: langchain-core==0.3.60 in c:\users\<USER>\documents\github\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 3)) (0.3.60)
Requirement already satisfied: langchain-ollama==0.3.3 in c:\users\<USER>\documents\github\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 4)) (0.3.3)
Requirement already satisfied: langchain-openai==0.3.17 in c:\users\<USER>\documents\github\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 5)) (0.3.17)
Requirement already satisfied: openai<2.0.0,>=1.68.2 in c:\users\<USER>\documents\github\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 6)) (1.81.0)
Requirement already satisfied: langchain-anthropic==0.3.13 in c:\users\<USER>\documents\github\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 7)) (0.3.13)
Requirement already satisfied: duckduckgo_search==8.0.2 in c:\users\<USER>\documents\github\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 8)) (8.0.2)
Requirement already satisfied: python-dateutil==2.9.0.post0 in c:\users\<USER>\d
ocuments\github\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 9)) (2.9.0.post0)
Requirement already satisfied: typing_extensions==4.13.2 in c:\users\<USER>\docu
ments\github\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 10)) (4.13.2)
Requirement already satisfied: justext==3.0.2 in c:\users\<USER>\documents\githu
b\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 11)) (3.0.2)
Requirement already satisfied: playwright==1.52.0 in c:\users\<USER>\documents\g
ithub\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 12)) (1.52.0)
Requirement already satisfied: beautifulsoup4==4.13.4 in c:\users\<USER>\documen
ts\github\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 13)) (4.13.4)
Requirement already satisfied: flask==3.1.1 in c:\users\<USER>\documents\github\
kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 14)) (3.1.1)
Requirement already satisfied: flask-cors==6.0.0 in c:\users\<USER>\documents\gi
thub\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 15)) (6.0.0)
Requirement already satisfied: flask-socketio==5.5.1 in c:\users\<USER>\document
s\github\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 16)) (5.5.1)
Requirement already satisfied: sqlalchemy==2.0.41 in c:\users\<USER>\documents\g
ithub\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 17)) (2.0.41)
Requirement already satisfied: wikipedia==1.4.0 in c:\users\<USER>\documents\git
hub\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 18)) (1.4.0)
Requirement already satisfied: arxiv==2.2.0 in c:\users\<USER>\documents\github\
kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 19)) (2.2.0)
Requirement already satisfied: pypdf==5.5.0 in c:\users\<USER>\documents\github\
kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 20)) (5.5.0)
Requirement already satisfied: sentence-transformers==4.1.0 in c:\users\<USER>\d
ocuments\github\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 21)) (4.1.0)
Requirement already satisfied: faiss-cpu==1.11.0 in c:\users\<USER>\documents\gi
thub\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 22)) (1.11.0)
Requirement already satisfied: pydantic==2.11.4 in c:\users\<USER>\documents\git
hub\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 23)) (2.11.4)
Requirement already satisfied: pydantic-settings==2.9.1 in c:\users\<USER>\docum
ents\github\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 24)) (2.9.1)
Requirement already satisfied: toml==0.10.2 in c:\users\<USER>\documents\github\
kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 25)) (0.10.2)
Requirement already satisfied: platformdirs==4.3.8 in c:\users\<USER>\documents\
github\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 26)) (4.3.8)
Requirement already satisfied: dynaconf==3.2.11 in c:\users\<USER>\documents\git
hub\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 27)) (3.2.11)
Requirement already satisfied: requests==2.32.3 in c:\users\<USER>\documents\git
hub\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 28)) (2.32.3)
Requirement already satisfied: tiktoken==0.9.0 in c:\users\<USER>\documents\gith
ub\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 29)) (0.9.0)
Requirement already satisfied: xmltodict==0.14.2 in c:\users\<USER>\documents\gi
thub\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 30)) (0.14.2)
Requirement already satisfied: lxml==5.4.0 in c:\users\<USER>\documents\github\k
itco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 31)) (5.4.0)
Requirement already satisfied: pdfplumber==0.11.6 in c:\users\<USER>\documents\g
ithub\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 32)) (0.11.6)
Requirement already satisfied: unstructured==0.17.2 in c:\users\<USER>\documents
\github\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 33)) (0.17.2)
Requirement already satisfied: google-search-results==2.4.2 in c:\users\<USER>\d
ocuments\github\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 34)) (2.4.2)
Requirement already satisfied: importlib-resources==6.5.2 in c:\users\<USER>\doc
uments\github\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 35)) (6.5.2)
Requirement already satisfied: setuptools==80.8.0 in c:\users\<USER>\documents\g
ithub\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 36)) (80.8.0)
Requirement already satisfied: flask-wtf==1.2.2 in c:\users\<USER>\documents\git
hub\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 37)) (1.2.2)
Requirement already satisfied: optuna==4.3.0 in c:\users\<USER>\documents\github
\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 38)) (4.3.0)
Requirement already satisfied: elasticsearch==9.0.1 in c:\users\<USER>\documents
\github\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 39)) (9.0.1)
Requirement already satisfied: methodtools==0.4.7 in c:\users\<USER>\documents\g
ithub\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 40)) (0.4.7)
Requirement already satisfied: loguru==0.7.3 in c:\users\<USER>\documents\github
\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 41)) (0.7.3)
Requirement already satisfied: matplotlib==3.10.3 in c:\users\<USER>\documents\g
ithub\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 42)) (3.10.3)
Requirement already satisfied: pandas==2.2.3 in c:\users\<USER>\documents\github
\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 43)) (2.2.3)
Requirement already satisfied: plotly==6.1.1 in c:\users\<USER>\documents\github
\kitco_research_ai\.venv\lib\site-packages (from -r requirements.txt (line 44)) (6.1.1)
Requirement already satisfied: langchain-text-splitters<1.0.0,>=0.3.8 in c:\user
s\dpatel\documents\github\kitco_research_ai\.venv\lib\site-packages (from langchain==0.3.25->-r requirements.txt (line 1)) (0.3.8)
Requirement already satisfied: langsmith<0.4,>=0.1.17 in c:\users\<USER>\documen
ts\github\kitco_research_ai\.venv\lib\site-packages (from langchain==0.3.25->-r requirements.txt (line 1)) (0.3.42)
Requirement already satisfied: PyYAML>=5.3 in c:\users\<USER>\documents\github\k
itco_research_ai\.venv\lib\site-packages (from langchain==0.3.25->-r requirements.txt (line 1)) (6.0.2)
Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in c:\users\<USER>\document
s\github\kitco_research_ai\.venv\lib\site-packages (from langchain-community==0.3.24->-r requirements.txt (line 2)) (3.12.1)
Requirement already satisfied: tenacity!=8.4.0,<10,>=8.1.0 in c:\users\<USER>\do
cuments\github\kitco_research_ai\.venv\lib\site-packages (from langchain-community==0.3.24->-r requirements.txt (line 2)) (9.1.2)
Requirement already satisfied: dataclasses-json<0.7,>=0.5.7 in c:\users\<USER>\d
ocuments\github\kitco_research_ai\.venv\lib\site-packages (from langchain-community==0.3.24->-r requirements.txt (line 2)) (0.6.7)
Requirement already satisfied: httpx-sse<1.0.0,>=0.4.0 in c:\users\<USER>\docume
nts\github\kitco_research_ai\.venv\lib\site-packages (from langchain-community==0.3.24->-r requirements.txt (line 2)) (0.4.0)
Requirement already satisfied: numpy>=2.1.0 in c:\users\<USER>\documents\github\
kitco_research_ai\.venv\lib\site-packages (from langchain-community==0.3.24->-r requirements.txt (line 2)) (2.2.6)
Requirement already satisfied: jsonpatch<2.0,>=1.33 in c:\users\<USER>\documents
\github\kitco_research_ai\.venv\lib\site-packages (from langchain-core==0.3.60->-r requirements.txt (line 3)) (1.33)
Requirement already satisfied: packaging<25,>=23.2 in c:\users\<USER>\documents\
github\kitco_research_ai\.venv\lib\site-packages (from langchain-core==0.3.60->-r requirements.txt (line 3)) (24.2)
Requirement already satisfied: ollama<1.0.0,>=0.4.8 in c:\users\<USER>\documents
\github\kitco_research_ai\.venv\lib\site-packages (from langchain-ollama==0.3.3->-r requirements.txt (line 4)) (0.4.8)
Requirement already satisfied: anthropic<1,>=0.51.0 in c:\users\<USER>\documents
\github\kitco_research_ai\.venv\lib\site-packages (from langchain-anthropic==0.3.13->-r requirements.txt (line 7)) (0.52.0)
Requirement already satisfied: click>=8.1.8 in c:\users\<USER>\documents\github\
kitco_research_ai\.venv\lib\site-packages (from duckduckgo_search==8.0.2->-r requirements.txt (line 8)) (8.2.1)
Requirement already satisfied: primp>=0.15.0 in c:\users\<USER>\documents\github
\kitco_research_ai\.venv\lib\site-packages (from duckduckgo_search==8.0.2->-r requirements.txt (line 8)) (0.15.0)
Requirement already satisfied: six>=1.5 in c:\users\<USER>\documents\github\kitc
o_research_ai\.venv\lib\site-packages (from python-dateutil==2.9.0.post0->-r requirements.txt (line 9)) (1.17.0)
Requirement already satisfied: pyee<14,>=13 in c:\users\<USER>\documents\github\
kitco_research_ai\.venv\lib\site-packages (from playwright==1.52.0->-r requirements.txt (line 12)) (13.0.0)
Requirement already satisfied: greenlet<4.0.0,>=3.1.1 in c:\users\<USER>\documen
ts\github\kitco_research_ai\.venv\lib\site-packages (from playwright==1.52.0->-r requirements.txt (line 12)) (3.2.2)
Requirement already satisfied: soupsieve>1.2 in c:\users\<USER>\documents\github
\kitco_research_ai\.venv\lib\site-packages (from beautifulsoup4==4.13.4->-r requirements.txt (line 13)) (2.7)
Requirement already satisfied: blinker>=1.9.0 in c:\users\<USER>\documents\githu
b\kitco_research_ai\.venv\lib\site-packages (from flask==3.1.1->-r requirements.txt (line 14)) (1.9.0)
Requirement already satisfied: itsdangerous>=2.2.0 in c:\users\<USER>\documents\
github\kitco_research_ai\.venv\lib\site-packages (from flask==3.1.1->-r requirements.txt (line 14)) (2.2.0)
Requirement already satisfied: jinja2>=3.1.2 in c:\users\<USER>\documents\github
\kitco_research_ai\.venv\lib\site-packages (from flask==3.1.1->-r requirements.txt (line 14)) (3.1.6)
Requirement already satisfied: markupsafe>=2.1.1 in c:\users\<USER>\documents\gi
thub\kitco_research_ai\.venv\lib\site-packages (from flask==3.1.1->-r requirements.txt (line 14)) (3.0.2)
Requirement already satisfied: werkzeug>=3.1.0 in c:\users\<USER>\documents\gith
ub\kitco_research_ai\.venv\lib\site-packages (from flask==3.1.1->-r requirements.txt (line 14)) (3.1.3)
Requirement already satisfied: python-socketio>=5.12.0 in c:\users\<USER>\docume
nts\github\kitco_research_ai\.venv\lib\site-packages (from flask-socketio==5.5.1->-r requirements.txt (line 16)) (5.13.0)
Requirement already satisfied: feedparser~=6.0.10 in c:\users\<USER>\documents\g
ithub\kitco_research_ai\.venv\lib\site-packages (from arxiv==2.2.0->-r requirements.txt (line 19)) (6.0.11)
Requirement already satisfied: transformers<5.0.0,>=4.41.0 in c:\users\<USER>\do
cuments\github\kitco_research_ai\.venv\lib\site-packages (from sentence-transformers==4.1.0->-r requirements.txt (line 21)) (4.52.2)
Requirement already satisfied: tqdm in c:\users\<USER>\documents\github\kitco_re
search_ai\.venv\lib\site-packages (from sentence-transformers==4.1.0->-r requirements.txt (line 21)) (4.67.1)
Requirement already satisfied: torch>=1.11.0 in c:\users\<USER>\documents\github
\kitco_research_ai\.venv\lib\site-packages (from sentence-transformers==4.1.0->-r requirements.txt (line 21)) (2.7.0)
Requirement already satisfied: scikit-learn in c:\users\<USER>\documents\github\
kitco_research_ai\.venv\lib\site-packages (from sentence-transformers==4.1.0->-r requirements.txt (line 21)) (1.6.1)
Requirement already satisfied: scipy in c:\users\<USER>\documents\github\kitco_r
esearch_ai\.venv\lib\site-packages (from sentence-transformers==4.1.0->-r requirements.txt (line 21)) (1.15.3)
Collecting huggingface-hub>=0.20.0 (from sentence-transformers==4.1.0->-r requirements.txt (line 21))
  Downloading huggingface_hub-0.32.2-py3-none-any.whl.metadata (14 kB)
Requirement already satisfied: Pillow in c:\users\<USER>\documents\github\kitco_
research_ai\.venv\lib\site-packages (from sentence-transformers==4.1.0->-r requirements.txt (line 21)) (11.2.1)
Requirement already satisfied: annotated-types>=0.6.0 in c:\users\<USER>\documen
ts\github\kitco_research_ai\.venv\lib\site-packages (from pydantic==2.11.4->-r requirements.txt (line 23)) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in c:\users\<USER>\document
s\github\kitco_research_ai\.venv\lib\site-packages (from pydantic==2.11.4->-r requirements.txt (line 23)) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in c:\users\<USER>\docum
ents\github\kitco_research_ai\.venv\lib\site-packages (from pydantic==2.11.4->-r requirements.txt (line 23)) (0.4.1)
Requirement already satisfied: python-dotenv>=0.21.0 in c:\users\<USER>\document
s\github\kitco_research_ai\.venv\lib\site-packages (from pydantic-settings==2.9.1->-r requirements.txt (line 24)) (1.1.0)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\docum
ents\github\kitco_research_ai\.venv\lib\site-packages (from requests==2.32.3->-r requirements.txt (line 28)) (3.4.2)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\documents\github\
kitco_research_ai\.venv\lib\site-packages (from requests==2.32.3->-r requirements.txt (line 28)) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\documents\g
ithub\kitco_research_ai\.venv\lib\site-packages (from requests==2.32.3->-r requirements.txt (line 28)) (2.4.0)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\documents\g
ithub\kitco_research_ai\.venv\lib\site-packages (from requests==2.32.3->-r requirements.txt (line 28)) (2025.4.26)
Requirement already satisfied: regex>=2022.1.18 in c:\users\<USER>\documents\git
hub\kitco_research_ai\.venv\lib\site-packages (from tiktoken==0.9.0->-r requirements.txt (line 29)) (2024.11.6)
Requirement already satisfied: pdfminer.six==20250327 in c:\users\<USER>\documen
ts\github\kitco_research_ai\.venv\lib\site-packages (from pdfplumber==0.11.6->-r requirements.txt (line 32)) (20250327)
Requirement already satisfied: pypdfium2>=4.18.0 in c:\users\<USER>\documents\gi
thub\kitco_research_ai\.venv\lib\site-packages (from pdfplumber==0.11.6->-r requirements.txt (line 32)) (4.30.1)
Collecting chardet (from unstructured==0.17.2->-r requirements.txt (line 33))
  Using cached chardet-5.2.0-py3-none-any.whl.metadata (3.4 kB)
Collecting filetype (from unstructured==0.17.2->-r requirements.txt (line 33))
  Using cached filetype-1.2.0-py2.py3-none-any.whl.metadata (6.5 kB)
Requirement already satisfied: python-magic in c:\users\<USER>\documents\github\
kitco_research_ai\.venv\lib\site-packages (from unstructured==0.17.2->-r requirements.txt (line 33)) (0.4.27)
Collecting nltk (from unstructured==0.17.2->-r requirements.txt (line 33))
  Using cached nltk-3.9.1-py3-none-any.whl.metadata (2.9 kB)
Collecting emoji (from unstructured==0.17.2->-r requirements.txt (line 33))
  Using cached emoji-2.14.1-py3-none-any.whl.metadata (5.7 kB)
Requirement already satisfied: python-iso639 in c:\users\<USER>\documents\github
\kitco_research_ai\.venv\lib\site-packages (from unstructured==0.17.2->-r requirements.txt (line 33)) (2025.2.18)
Collecting langdetect (from unstructured==0.17.2->-r requirements.txt (line 33))
  Using cached langdetect-1.0.9-py3-none-any.whl
Requirement already satisfied: rapidfuzz in c:\users\<USER>\documents\github\kit
co_research_ai\.venv\lib\site-packages (from unstructured==0.17.2->-r requirements.txt (line 33)) (3.13.0)
Collecting backoff (from unstructured==0.17.2->-r requirements.txt (line 33))
  Using cached backoff-2.2.1-py3-none-any.whl.metadata (14 kB)
Requirement already satisfied: unstructured-client in c:\users\<USER>\documents\
github\kitco_research_ai\.venv\lib\site-packages (from unstructured==0.17.2->-r requirements.txt (line 33)) (0.35.0)
Requirement already satisfied: wrapt in c:\users\<USER>\documents\github\kitco_r
esearch_ai\.venv\lib\site-packages (from unstructured==0.17.2->-r requirements.txt (line 33)) (1.17.2)
Requirement already satisfied: psutil in c:\users\<USER>\documents\github\kitco_
research_ai\.venv\lib\site-packages (from unstructured==0.17.2->-r requirements.txt (line 33)) (7.0.0)
Requirement already satisfied: python-oxmsg in c:\users\<USER>\documents\github\
kitco_research_ai\.venv\lib\site-packages (from unstructured==0.17.2->-r requirements.txt (line 33)) (0.0.2)
Collecting html5lib (from unstructured==0.17.2->-r requirements.txt (line 33))
  Using cached html5lib-1.1-py2.py3-none-any.whl.metadata (16 kB)
Requirement already satisfied: wtforms in c:\users\<USER>\documents\github\kitco
_research_ai\.venv\lib\site-packages (from flask-wtf==1.2.2->-r requirements.txt (line 37)) (3.2.1)
Collecting alembic>=1.5.0 (from optuna==4.3.0->-r requirements.txt (line 38))
  Using cached alembic-1.16.1-py3-none-any.whl.metadata (7.3 kB)
Collecting colorlog (from optuna==4.3.0->-r requirements.txt (line 38))
  Using cached colorlog-6.9.0-py3-none-any.whl.metadata (10 kB)
Requirement already satisfied: elastic-transport<9,>=8.15.1 in c:\users\<USER>\d
ocuments\github\kitco_research_ai\.venv\lib\site-packages (from elasticsearch==9.0.1->-r requirements.txt (line 39)) (8.17.1)
Requirement already satisfied: wirerope>=0.4.7 in c:\users\<USER>\documents\gith
ub\kitco_research_ai\.venv\lib\site-packages (from methodtools==0.4.7->-r requirements.txt (line 40)) (1.0.0)
Requirement already satisfied: colorama>=0.3.4 in c:\users\<USER>\documents\gith
ub\kitco_research_ai\.venv\lib\site-packages (from loguru==0.7.3->-r requirements.txt (line 41)) (0.4.6)
Requirement already satisfied: win32-setctime>=1.0.0 in c:\users\<USER>\document
s\github\kitco_research_ai\.venv\lib\site-packages (from loguru==0.7.3->-r requirements.txt (line 41)) (1.2.0)
Requirement already satisfied: contourpy>=1.0.1 in c:\users\<USER>\documents\git
hub\kitco_research_ai\.venv\lib\site-packages (from matplotlib==3.10.3->-r requirements.txt (line 42)) (1.3.2)
Requirement already satisfied: cycler>=0.10 in c:\users\<USER>\documents\github\
kitco_research_ai\.venv\lib\site-packages (from matplotlib==3.10.3->-r requirements.txt (line 42)) (0.12.1)
Requirement already satisfied: fonttools>=4.22.0 in c:\users\<USER>\documents\gi
thub\kitco_research_ai\.venv\lib\site-packages (from matplotlib==3.10.3->-r requirements.txt (line 42)) (4.58.0)
Requirement already satisfied: kiwisolver>=1.3.1 in c:\users\<USER>\documents\gi
thub\kitco_research_ai\.venv\lib\site-packages (from matplotlib==3.10.3->-r requirements.txt (line 42)) (1.4.8)
Requirement already satisfied: pyparsing>=2.3.1 in c:\users\<USER>\documents\git
hub\kitco_research_ai\.venv\lib\site-packages (from matplotlib==3.10.3->-r requirements.txt (line 42)) (3.2.3)
Requirement already satisfied: pytz>=2020.1 in c:\users\<USER>\documents\github\
kitco_research_ai\.venv\lib\site-packages (from pandas==2.2.3->-r requirements.txt (line 43)) (2025.2)
Requirement already satisfied: tzdata>=2022.7 in c:\users\<USER>\documents\githu
b\kitco_research_ai\.venv\lib\site-packages (from pandas==2.2.3->-r requirements.txt (line 43)) (2025.2)
Collecting narwhals>=1.15.1 (from plotly==6.1.1->-r requirements.txt (line 44))
  Downloading narwhals-1.41.0-py3-none-any.whl.metadata (11 kB)
Collecting cryptography>=36.0.0 (from pdfminer.six==20250327->pdfplumber==0.11.6->-r requirements.txt (line 32))
  Downloading cryptography-45.0.3-cp311-abi3-win_amd64.whl.metadata (5.7 kB)
Requirement already satisfied: anyio<5,>=3.5.0 in c:\users\<USER>\documents\gith
ub\kitco_research_ai\.venv\lib\site-packages (from openai<2.0.0,>=1.68.2->-r requirements.txt (line 6)) (4.9.0)
Requirement already satisfied: distro<2,>=1.7.0 in c:\users\<USER>\documents\git
hub\kitco_research_ai\.venv\lib\site-packages (from openai<2.0.0,>=1.68.2->-r requirements.txt (line 6)) (1.9.0)
Requirement already satisfied: httpx<1,>=0.23.0 in c:\users\<USER>\documents\git
hub\kitco_research_ai\.venv\lib\site-packages (from openai<2.0.0,>=1.68.2->-r requirements.txt (line 6)) (0.28.1)
Requirement already satisfied: jiter<1,>=0.4.0 in c:\users\<USER>\documents\gith
ub\kitco_research_ai\.venv\lib\site-packages (from openai<2.0.0,>=1.68.2->-r requirements.txt (line 6)) (0.10.0)
Requirement already satisfied: sniffio in c:\users\<USER>\documents\github\kitco
_research_ai\.venv\lib\site-packages (from openai<2.0.0,>=1.68.2->-r requirements.txt (line 6)) (1.3.1)
Requirement already satisfied: aiohappyeyeballs>=2.5.0 in c:\users\<USER>\docume
nts\github\kitco_research_ai\.venv\lib\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community==0.3.24->-r requirements.txt (line 2)) (2.6.1)
Requirement already satisfied: aiosignal>=1.1.2 in c:\users\<USER>\documents\git
hub\kitco_research_ai\.venv\lib\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community==0.3.24->-r requirements.txt (line 2)) (1.3.2)
Requirement already satisfied: attrs>=17.3.0 in c:\users\<USER>\documents\github
\kitco_research_ai\.venv\lib\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community==0.3.24->-r requirements.txt (line 2)) (25.3.0)
Requirement already satisfied: frozenlist>=1.1.1 in c:\users\<USER>\documents\gi
thub\kitco_research_ai\.venv\lib\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community==0.3.24->-r requirements.txt (line 2)) (1.6.0)
Requirement already satisfied: multidict<7.0,>=4.5 in c:\users\<USER>\documents\
github\kitco_research_ai\.venv\lib\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community==0.3.24->-r requirements.txt (line 2)) (6.4.4)
Requirement already satisfied: propcache>=0.2.0 in c:\users\<USER>\documents\git
hub\kitco_research_ai\.venv\lib\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community==0.3.24->-r requirements.txt (line 2)) (0.3.1)
Requirement already satisfied: yarl<2.0,>=1.17.0 in c:\users\<USER>\documents\gi
thub\kitco_research_ai\.venv\lib\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community==0.3.24->-r requirements.txt (line 2)) (1.20.0)
Collecting Mako (from alembic>=1.5.0->optuna==4.3.0->-r requirements.txt (line 38))
  Using cached mako-1.3.10-py3-none-any.whl.metadata (2.9 kB)
Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in c:\users\<USER>\doc
uments\github\kitco_research_ai\.venv\lib\site-packages (from dataclasses-json<0.7,>=0.5.7->langchain-community==0.3.24->-r requirements.txt (line 2)) (3.26.1)
Requirement already satisfied: typing-inspect<1,>=0.4.0 in c:\users\<USER>\docum
ents\github\kitco_research_ai\.venv\lib\site-packages (from dataclasses-json<0.7,>=0.5.7->langchain-community==0.3.24->-r requirements.txt (line 2)) (0.9.0)
Requirement already satisfied: sgmllib3k in c:\users\<USER>\documents\github\kit
co_research_ai\.venv\lib\site-packages (from feedparser~=6.0.10->arxiv==2.2.0->-r requirements.txt (line 19)) (1.0.0)
Requirement already satisfied: httpcore==1.* in c:\users\<USER>\documents\github
\kitco_research_ai\.venv\lib\site-packages (from httpx<1,>=0.23.0->openai<2.0.0,>=1.68.2->-r requirements.txt (line 6)) (1.0.9)
Requirement already satisfied: h11>=0.16 in c:\users\<USER>\documents\github\kit
co_research_ai\.venv\lib\site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai<2.0.0,>=1.68.2->-r requirements.txt (line 6)) (0.16.0)
Collecting filelock (from huggingface-hub>=0.20.0->sentence-transformers==4.1.0->-r requirements.txt (line 21))
  Using cached filelock-3.18.0-py3-none-any.whl.metadata (2.9 kB)
Collecting fsspec>=2023.5.0 (from huggingface-hub>=0.20.0->sentence-transformers==4.1.0->-r requirements.txt (line 21))
  Downloading fsspec-2025.5.1-py3-none-any.whl.metadata (11 kB)
Requirement already satisfied: jsonpointer>=1.9 in c:\users\<USER>\documents\git
hub\kitco_research_ai\.venv\lib\site-packages (from jsonpatch<2.0,>=1.33->langchain-core==0.3.60->-r requirements.txt (line 3)) (3.0.0)
Requirement already satisfied: orjson<4.0.0,>=3.9.14 in c:\users\<USER>\document
s\github\kitco_research_ai\.venv\lib\site-packages (from langsmith<0.4,>=0.1.17->langchain==0.3.25->-r requirements.txt (line 1)) (3.10.18)
Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in c:\users\<USER>\documents\github\kitco_research_ai\.venv\lib\site-packages (from langsmith<0.4,>=0.1.17->langchain==0.3.25->-r requirements.txt (line 1)) (1.0.0)
Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in c:\users\<USER>\docu
ments\github\kitco_research_ai\.venv\lib\site-packages (from langsmith<0.4,>=0.1.17->langchain==0.3.25->-r requirements.txt (line 1)) (0.23.0)
Requirement already satisfied: lxml_html_clean in c:\users\<USER>\documents\gith
ub\kitco_research_ai\.venv\lib\site-packages (from lxml[html_clean]>=4.4.2->justext==3.0.2->-r requirements.txt (line 11)) (0.4.2)
Requirement already satisfied: bidict>=0.21.0 in c:\users\<USER>\documents\githu
b\kitco_research_ai\.venv\lib\site-packages (from python-socketio>=5.12.0->flask-socketio==5.5.1->-r requirements.txt (line 16)) (0.23.1)
Requirement already satisfied: python-engineio>=4.11.0 in c:\users\<USER>\docume
nts\github\kitco_research_ai\.venv\lib\site-packages (from python-socketio>=5.12.0->flask-socketio==5.5.1->-r requirements.txt (line 16)) (4.12.1)
Requirement already satisfied: sympy>=1.13.3 in c:\users\<USER>\documents\github
\kitco_research_ai\.venv\lib\site-packages (from torch>=1.11.0->sentence-transformers==4.1.0->-r requirements.txt (line 21)) (1.14.0)
Collecting networkx (from torch>=1.11.0->sentence-transformers==4.1.0->-r requirements.txt (line 21))
  Using cached networkx-3.4.2-py3-none-any.whl.metadata (6.3 kB)
Requirement already satisfied: tokenizers<0.22,>=0.21 in c:\users\<USER>\documen
ts\github\kitco_research_ai\.venv\lib\site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers==4.1.0->-r requirements.txt (line 21)) (0.21.1)
Requirement already satisfied: safetensors>=0.4.3 in c:\users\<USER>\documents\g
ithub\kitco_research_ai\.venv\lib\site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers==4.1.0->-r requirements.txt (line 21)) (0.5.3)
Requirement already satisfied: webencodings in c:\users\<USER>\documents\github\
kitco_research_ai\.venv\lib\site-packages (from html5lib->unstructured==0.17.2->-r requirements.txt (line 33)) (0.5.1)
Collecting joblib (from nltk->unstructured==0.17.2->-r requirements.txt (line 33))
  Downloading joblib-1.5.1-py3-none-any.whl.metadata (5.6 kB)
Collecting olefile (from python-oxmsg->unstructured==0.17.2->-r requirements.txt (line 33))
  Using cached olefile-0.47-py2.py3-none-any.whl.metadata (9.7 kB)
Requirement already satisfied: threadpoolctl>=3.1.0 in c:\users\<USER>\documents
\github\kitco_research_ai\.venv\lib\site-packages (from scikit-learn->sentence-transformers==4.1.0->-r requirements.txt (line 21)) (3.6.0)
Collecting aiofiles>=24.1.0 (from unstructured-client->unstructured==0.17.2->-r requirements.txt (line 33))
  Using cached aiofiles-24.1.0-py3-none-any.whl.metadata (10 kB)
Collecting nest-asyncio>=1.6.0 (from unstructured-client->unstructured==0.17.2->-r requirements.txt (line 33))
  Using cached nest_asyncio-1.6.0-py3-none-any.whl.metadata (2.8 kB)
Collecting cffi>=1.14 (from cryptography>=36.0.0->pdfminer.six==20250327->pdfplumber==0.11.6->-r requirements.txt (line 32))
  Using cached cffi-1.17.1-cp313-cp313-win_amd64.whl.metadata (1.6 kB)
Requirement already satisfied: simple-websocket>=0.10.0 in c:\users\<USER>\docum
ents\github\kitco_research_ai\.venv\lib\site-packages (from python-engineio>=4.1
1.0->python-socketio>=5.12.0->flask-socketio==5.5.1->-r requirements.txt (line 16)) (1.1.0)
Collecting mpmath<1.4,>=1.1.0 (from sympy>=1.13.3->torch>=1.11.0->sentence-transformers==4.1.0->-r requirements.txt (line 21))
  Using cached mpmath-1.3.0-py3-none-any.whl.metadata (8.6 kB)
Requirement already satisfied: mypy-extensions>=0.3.0 in c:\users\<USER>\documen
ts\github\kitco_research_ai\.venv\lib\site-packages (from typing-inspect<1,>=0.4
.0->dataclasses-json<0.7,>=0.5.7->langchain-community==0.3.24->-r requirements.txt (line 2)) (1.1.0)
Requirement already satisfied: pycparser in c:\users\<USER>\documents\github\kit
co_research_ai\.venv\lib\site-packages (from cffi>=1.14->cryptography>=36.0.0->pdfminer.six==20250327->pdfplumber==0.11.6->-r requirements.txt (line 32)) (2.22)
Requirement already satisfied: wsproto in c:\users\<USER>\documents\github\kitco
_research_ai\.venv\lib\site-packages (from simple-websocket>=0.10.0->python-engi
neio>=4.11.0->python-socketio>=5.12.0->flask-socketio==5.5.1->-r requirements.txt (line 16)) (1.2.0)
Using cached alembic-1.16.1-py3-none-any.whl (242 kB)
Downloading huggingface_hub-0.32.2-py3-none-any.whl (509 kB)
Downloading narwhals-1.41.0-py3-none-any.whl (357 kB)
Using cached backoff-2.2.1-py3-none-any.whl (15 kB)
Using cached chardet-5.2.0-py3-none-any.whl (199 kB)
Using cached colorlog-6.9.0-py3-none-any.whl (11 kB)
Using cached emoji-2.14.1-py3-none-any.whl (590 kB)
Using cached filetype-1.2.0-py2.py3-none-any.whl (19 kB)
Using cached html5lib-1.1-py2.py3-none-any.whl (112 kB)
Using cached nltk-3.9.1-py3-none-any.whl (1.5 MB)
Using cached aiofiles-24.1.0-py3-none-any.whl (15 kB)
Downloading cryptography-45.0.3-cp311-abi3-win_amd64.whl (3.4 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 3.4/3.4 MB 52.9 MB/s eta 0:00:00
Downloading fsspec-2025.5.1-py3-none-any.whl (199 kB)
Downloading joblib-1.5.1-py3-none-any.whl (307 kB)
Using cached nest_asyncio-1.6.0-py3-none-any.whl (5.2 kB)
Using cached filelock-3.18.0-py3-none-any.whl (16 kB)
Using cached mako-1.3.10-py3-none-any.whl (78 kB)
Using cached networkx-3.4.2-py3-none-any.whl (1.7 MB)
Using cached olefile-0.47-py2.py3-none-any.whl (114 kB)
Using cached cffi-1.17.1-cp313-cp313-win_amd64.whl (182 kB)
Using cached mpmath-1.3.0-py3-none-any.whl (536 kB)
Installing collected packages: mpmath, filetype, olefile, networkx, nest-asyncio
, narwhals, Mako, langdetect, joblib, html5lib, fsspec, filelock, emoji, colorlo
g, chardet, cffi, backoff, aiofiles, nltk, huggingface-hub, cryptography, alembic
Successfully installed Mako-1.3.10 aiofiles-24.1.0 alembic-1.16.1 backoff-2.2.1
cffi-1.17.1 chardet-5.2.0 colorlog-6.9.0 cryptography-45.0.3 emoji-2.14.1 filelo
ck-3.18.0 filetype-1.2.0 fsspec-2025.5.1 html5lib-1.1 huggingface-hub-0.32.2 job
lib-1.5.1 langdetect-1.0.9 mpmath-1.3.0 narwhals-1.41.0 nest-asyncio-1.6.0 networkx-3.4.2 nltk-3.9.1 olefile-0.47
[notice] A new release of pip is available: 25.0.1 -> 25.1.1
[notice] To update, run: python.exe -m pip install --upgrade pip