[build-system]
requires = ["pdm-backend"]
build-backend = "pdm.backend"

[project]
name = "kitco-research-ai"
dynamic = ["version"]
description = "AI-powered research assistant with deep, iterative analysis using LLMs and web searches"
readme = "README.md"
requires-python = ">=3.10"
license = {file = "LICENSE"}
authors = [
    {name = "LearningCircuit", email = "<EMAIL>"},
    {name = "HashedViking", email = "<EMAIL>"},
]
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
dependencies = [
    "langchain>=0.3.18",
    "langchain-community>=0.3.17",
    "langchain-core>=0.3.34",
    "langchain-ollama>=0.2.3",
    "langchain-openai>=0.3.5",
    "openai>=1.68.2,<2.0.0",
    "langchain-anthropic>=0.3.13",
    "duckduckgo_search>=7.3.2",
    "python-dateutil>=2.9.0",
    "typing_extensions>=4.12.2",
    "justext",
    "playwright",
    "beautifulsoup4",
    "flask>=3.1.0",
    "flask-cors>=3.0.10",
    "flask-socketio>=5.1.1",
    "sqlalchemy>=1.4.23",
    "wikipedia",
    "arxiv>=1.4.3",
    "pypdf",
    "sentence-transformers",
    "faiss-cpu",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "toml>=0.10.2",
    "platformdirs>=3.0.0",
    "dynaconf",
    "requests>=2.28.0",
    "tiktoken>=0.4.0",
    "xmltodict>=0.13.0",
    "lxml>=4.9.2",
    "pdfplumber>=0.9.0",
    "unstructured>=0.10.0",
    "google-search-results",
    "importlib-resources>=6.5.2",
    "setuptools>=78.1.0",
    "flask-wtf>=1.2.2",
    "optuna>=4.3.0",
    "elasticsearch==8.14.0",
    "methodtools>=0.4.7",
    "loguru>=0.7.3",
    "matplotlib>=3.10.3",
    "pandas>=2.2.3",
    "plotly>=6.0.1",
    "kaleido==0.1.0",
]

[project.urls]
"Homepage" = "https://github.com/LearningCircuit/kitco-research-ai"
"Bug Tracker" = "https://github.com/LearningCircuit/kitco-research-ai/issues"

[project.scripts]
krai = "kitco_research_ai.main:main"
krai-web = "kitco_research_ai.web.app:main"

[tool.setuptools]
include-package-data = true

[tool.setuptools.package-data]
"kitco_research_ai.web" = ["templates/*", "static/*", "static/**/*"]
"kitco_research_ai.defaults" = ["*.toml", "*.py", "*.template", ".env.template"]


[tool.pdm]
distribution = true
version = { source = "file", path = "src/kitco_research_ai/__version__.py" }
[[tool.pdm.source]]
url = "https://download.pytorch.org/whl/cpu"
name = "torch"
include_packages = ["torch", "torch*"]

[dependency-groups]
dev = [
    "isort>=6.0.1",
    "black>=25.1.0",
    "pre-commit>=4.2.0",
    "flake8>=7.1.2",
    "jupyter>=1.1.1",
    "cookiecutter>=2.6.0",
    "pandas>=2.2.3",
    "optuna>=4.3.0",
    "pytest-mock>=3.14.0",
]
